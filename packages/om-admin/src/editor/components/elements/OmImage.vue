<template lang="pug">
element-resize(
  v-if="!isSSR"
  :uid="item.uid"
  elementType="OmImage"
  :disableHeightResize="true"
  @element-resize="$emit('element-resize', $event)"
)
  .om-image(:id="`${item.uid}_align`" :style="imageStyle" :class="lowContrastClasses")
    img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
    a(
      v-else-if="isImageRedirect"
      :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
      :data-om-image-id="item.uid"
      :href="redirectUrl"
      :target="target"
      :data-om-settings="redirectSettings"
    )
      picture
        source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
        source(:srcset="url")
        img.om-image(:id="item.uid" :src="url")
    picture(v-else="")
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
.om-image(:id="`${item.uid}_align`" :style="imageStyle" v-else="" :class="lowContrastClasses")
  img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
  a(
    v-else-if="isImageRedirect"
    :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
    :data-om-image-id="item.uid"
    :href="redirectUrl"
    :target="target"
    :data-om-settings="redirectSettings"
  )
    picture
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  picture(v-else="")
    source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
    source(:srcset="url")
    img.om-image(:id="item.uid" :src="url")
</template>
<script>
  import { FastAverageColor } from 'fast-average-color';
  import tinycolor from 'tinycolor2';
  import ElementResize from '@/editor/components/visualizers/ElementResize.vue';
  import lazyImage from '@/mixins/lazyImage';
  import image from '@/mixins/image';

  const LOW_CONTRAST_THRESHOLD = 1.75;

  export default {
    components: { ElementResize },
    mixins: [lazyImage, image],
    props: ['isSSR'],
    data: () => ({
      desktopContrast: null,
      mobileContrast: null,
      lowContrastDesktop: false,
      lowContrastMobile: false,
    }),
    computed: {
      lowContrastClasses() {
        return {
          [`low-contrast-desktop-${this.lowContrastDesktop}`]: !!this.lowContrastDesktop,
          [`low-contrast-mobile-${this.lowContrastMobile}`]: !!this.lowContrastMobile,
        };
      },
    },
    watch: {
      url: {
        handler(url) {
          this.getDominantColorContrasts(url, 'desktop');
        },
        immediate: true,
      },
      mobileUrl: {
        handler(url) {
          this.getDominantColorContrasts(url, 'mobile');
        },
        immediate: true,
      },
      desktopContrast: {
        handler(contrast) {
          if (!contrast?.onWhite && !contrast?.onBlack) return;
          if (
            contrast?.onWhite < LOW_CONTRAST_THRESHOLD &&
            contrast?.onBlack > LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastDesktop = 'white';
          } else if (
            contrast?.onWhite > LOW_CONTRAST_THRESHOLD &&
            contrast?.onBlack < LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastDesktop = 'black';
          } else {
            this.lowContrastDesktop = false;
          }
        },
        immediate: true,
        deep: true,
      },
      mobileContrast: {
        handler(contrast) {
          if (!contrast?.onWhite && !contrast?.onBlack) return;
          if (
            contrast.onWhite < LOW_CONTRAST_THRESHOLD &&
            contrast.onBlack > LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastMobile = 'white';
          } else if (
            contrast.onWhite > LOW_CONTRAST_THRESHOLD &&
            contrast.onBlack < LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastMobile = 'black';
          } else {
            this.lowContrastMobile = false;
          }
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {
      if (!this.desktopUrl && !this.mobileUrl) return;
      this.getDominantColorContrasts(this.url, 'desktop');
      this.getDominantColorContrasts(this.mobileUrl, 'mobile');
    },
    methods: {
      async getDominantColorContrasts(url, type) {
        if (!url) return;

        try {
          const fac = new FastAverageColor();
          const color = await fac.getColorAsync(url, {
            algorithm: 'dominant',
            ignoredColor: [
              [255, 255, 255, 0, 64],
              [255, 255, 255, 1, 64],
              [0, 0, 0, 0, 64],
              [0, 0, 0, 1, 64],
            ],
          });

          this[`${type}Contrast`] = this.calculateContrast(color.hex);
          console.log('Contrast', this[`${type}Contrast`], color.hex);
        } catch (e) {
          console.log(`[AVG COLOR][${type}]`, e);
        }
      },

      calculateContrast(color) {
        const avg = tinycolor(color);

        return {
          onWhite: tinycolor.readability(avg, tinycolor('#ffffff')),
          onBlack: tinycolor.readability(avg, tinycolor('#000000')),
        };
      },
    },
  };
</script>

<style lang="sass">
  .om-image
    &.low-contrast-desktop
      &-black
        filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
      &-white
        filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
    @media screen and (max-width: 576px)
      &.low-contrast-mobile
        &-black
          filter: drop-shadow(0px 0px 3px rgba(255, 255, 255, 0.4))
        &-white
          filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
</style>
