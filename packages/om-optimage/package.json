{"name": "om-optimage", "version": "1.0.0", "description": "", "main": "index.js", "private": true, "scripts": {"installNdev": "npm install && npm run dev", "start": "node ./src/index.js", "dev": "NODE_ENV=development nodemon -L ./src/index.js", "lint": "eslint --ext .js .", "test": "echo \"Error: no test specified\" && exit 1"}, "config": {"libvips": "8.8.1"}, "dependencies": {"@imgly/background-removal-node": "^1.4.5", "body-parser": "^1.18.3", "cors": "^2.8.5", "dotenv": "^7.0.0", "dotenv-flow": "^3.2.0", "express": "^4.16.4", "express-pino-logger": "^4.0.0", "get-stream": "^5.0.0", "jsonwebtoken": "^8.5.0", "multer": "^1.4.1", "pino": "^5.11.1", "sharp": "^0.33.5"}, "devDependencies": {"nodemon": "^1.18.10", "pino-pretty": "^2.5.0"}}