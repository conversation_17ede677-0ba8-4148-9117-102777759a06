# OM Optimage

A powerful image processing service that provides image optimization, transformation, and background removal capabilities.

## Features

- **Image Optimization**: Compress and optimize images with quality control
- **Image Transformations**: Resize, crop, rotate, flip, and trim images
- **Background Removal**: Remove backgrounds from images using AI-powered models
- **Format Conversion**: Convert between different image formats (JPEG, PNG, WebP)
- **URL Processing**: Process images directly from URLs
- **RESTful API**: Easy-to-use HTTP endpoints

## Installation

```bash
npm install
```

## Environment Variables

Create a `.env` file with the following variables:

```env
NODE_PORT=3000
NODE_ENV=development
MAX_FILE_SIZE=10485760
ALLOWED_MIME_TYPES=image/(jpeg|jpg|png|webp|gif|bmp|tiff)
DEFAULT_IMAGE_QUALITY=80
cors=1
```

## Usage

### Start the Service

```bash
# Development
npm run dev

# Production
npm start
```

## API Endpoints

### Health Check

```
GET /healthz
```

Returns service health status.

### Image Processing

```
POST /
```

Process images with various transformations and optimizations.

**Parameters:**

- `src` (file): Image file to process
- `url` (string): URL of image to process (alternative to file upload)
- `quality` (number): Output quality (1-100)
- `format` (string): Output format ('jpeg' or 'png')
- `width` (number): Resize width
- `height` (number): Resize height
- `cropLeft`, `cropTop`, `cropWidth`, `cropHeight` (numbers): Crop parameters
- `rotate` (number): Rotation angle in degrees
- `scaleX`, `scaleY` (number): Scale factors (-1 or 1)
- `trim` (boolean): Remove transparent/white borders

### Background Removal

```
POST /remove-background
```

Remove backgrounds from images using AI-powered models.

**Parameters:**

- `src` (file): Image file to process
- `url` (string): URL of image to process (alternative to file upload)
- `removeBackground` (boolean): Enable background removal (automatically set to true for this endpoint)
- `bgModel` (string): Model to use ('small' or 'medium', default: 'medium')
- `bgFormat` (string): Output format ('image/png', 'image/jpeg', 'image/webp', 'image/x-rgba8', default: 'image/png')
- `bgQuality` (number): Output quality (0-1, default: 0.8)
- `bgType` (string): Output type ('foreground', 'background', 'mask', default: 'foreground')

**Background Removal Options:**

- **Models:**

  - `small` (~40MB): Faster processing, may show some artifacts
  - `medium` (~80MB): Better quality, default option

- **Output Types:**

  - `foreground`: Returns the subject with background removed (default)
  - `background`: Returns only the background
  - `mask`: Returns the alpha mask used for removal

- **Output Formats:**
  - `image/png`: Best for transparency (default)
  - `image/jpeg`: Smaller file size, no transparency
  - `image/webp`: Modern format with good compression
  - `image/x-rgba8`: Raw RGBA data

### Background Removal Validation

```
GET /remove-background/validate
```

Validate background removal parameters without processing.

**Query Parameters:** Same as background removal endpoint

### Background Removal Info

```
GET /remove-background/info
```

Get information about available background removal options.

## Examples

### Basic Image Processing

```bash
# Upload and optimize image
curl -X POST \
  -F "src=@image.jpg" \
  -F "quality=80" \
  -F "format=jpeg" \
  http://localhost:3000/
```

### Background Removal

```bash
# Remove background with default settings
curl -X POST \
  -F "src=@portrait.jpg" \
  http://localhost:3000/remove-background \
  --output result.png

# Remove background with custom settings
curl -X POST \
  -F "src=@portrait.jpg" \
  -F "bgModel=small" \
  -F "bgFormat=image/webp" \
  -F "bgQuality=0.9" \
  http://localhost:3000/remove-background \
  --output result.webp

# Get only the background
curl -X POST \
  -F "src=@portrait.jpg" \
  -F "bgType=background" \
  http://localhost:3000/remove-background \
  --output background.png

# Get the alpha mask
curl -X POST \
  -F "src=@portrait.jpg" \
  -F "bgType=mask" \
  http://localhost:3000/remove-background \
  --output mask.png
```

### Process Image from URL

```bash
# Remove background from image URL
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/image.jpg"}' \
  http://localhost:3000/remove-background \
  --output result.png
```

### Combined Processing

You can also use background removal with the main endpoint:

```bash
# Remove background and resize
curl -X POST \
  -F "src=@image.jpg" \
  -F "removeBackground=true" \
  -F "width=800" \
  -F "height=600" \
  http://localhost:3000/ \
  --output processed.png
```

## Response Headers

The service includes helpful response headers:

- `X-Image-Width`: Output image width
- `X-Image-Height`: Output image height
- `X-Processing-Type`: Type of processing applied (e.g., 'background-removal')

## Error Handling

The service returns appropriate HTTP status codes:

- `200`: Success
- `400`: Bad request (invalid parameters)
- `422`: Unprocessable entity (invalid image format)
- `500`: Internal server error
- `502`: Bad gateway (failed to download model/image)
- `504`: Gateway timeout (processing timeout)

Error responses include:

```json
{
  "status": 400,
  "error": "Error message",
  "type": "error-type"
}
```

## Performance Notes

### Background Removal

- **First Run**: The first background removal request will download the AI model (~40-80MB), which may take time depending on your internet connection
- **Subsequent Runs**: Models are cached for faster processing
- **Memory Usage**: Background removal is memory-intensive; consider the image size and available RAM
- **Processing Time**: Varies based on image size and model choice (small model is faster)

### Recommendations

- Use the `small` model for faster processing when quality is less critical
- Use `image/webp` format for better compression while maintaining quality
- Consider image size limits based on your server's memory capacity
- Implement proper timeout handling for large images

## Dependencies

- **Sharp**: High-performance image processing
- **@imgly/background-removal-node**: AI-powered background removal
- **Express**: Web framework
- **Multer**: File upload handling

## License

[Add your license information here]
