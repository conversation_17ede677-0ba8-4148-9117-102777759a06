const http = require('http');
const https = require('https');
const bodyParser = require('body-parser');
const multer = require('multer');
const toBuffer = require('get-stream').buffer;
const ImageInput = require('../services/imageProcessor').ImageInput;

const init = (req, _, next) => {
  req.input = {
    ...req.body,
    ...req.query,
  };

  next();
};

const downloadUrl = (req, res, next) => {
  if (req.input.url) {
    const { url } = req.input;
    let httpLib = http;

    if (/^https/.test(req.input.url)) {
      httpLib = https;
    }

    req.log.debug('source image will be downloaded from', url);
    httpLib
      .get(url, async (file) => {
        const buffer = await toBuffer(file);
        req.file = {
          size: buffer.byteLength,
          mimetype: file.headers['content-type'],
          buffer,
        };

        req.log.debug(
          `downloaded source image (${req.file.mimetype}; ${req.file.size} bytes) from ${url}`,
        );

        next();
      })
      .on('error', (err) => {
        req.log.error(err, 'failed to download source image from', url);
        res.status(502).json({ status: 502, error: 'failed to download source image' });
      });

    return;
  }

  next();
};

const multipart = () => {
  const parser = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: process.env.MAX_FILE_SIZE,
      files: 1,
    },
  }).single('src');

  return (req, res, next) => {
    parser(req, res, (err) => {
      if (err) {
        req.log.error(err);

        return res
          .status(500)
          .json({ status: 500, error: 'Error while parsing multipart data' })
          .end();
      }

      next();
    });
  };
};

const toImageInput = (req, _, next) => {
  const { input, file } = req;

  if (input && file) {
    req.log.debug(input, 'creating image input for', file.mimetype);
    req.input = new ImageInput(file.buffer, input);

    return next();
  }

  delete req.input;
  delete req.file;
};

const { MAX_FILE_SIZE, ALLOWED_MIME_TYPES } = process.env;
const allowedMimeTypes = new RegExp(ALLOWED_MIME_TYPES);
const allowedFormats = new Set(['png', 'jpeg']);

const validationError = (res, msg, status = 422) => {
  res.status(status).json({ status, error: msg }).end();
};

const fileSize = (req, res, next) => {
  if (MAX_FILE_SIZE > 0) {
    if (req.file && req.file.size > MAX_FILE_SIZE) {
      return validationError(res, `Source file size exceeds the maximum (${MAX_FILE_SIZE} bytes)`);
    }
  }

  next();
};

const fileMime = (req, res, next) => {
  if (
    req.file &&
    !allowedMimeTypes.test(req.file.mimetype) &&
    req.file.mimetype !== 'application/octet-stream'
  ) {
    return validationError(res, `Unsupported image format: "${req.file.mimetype}"`);
  }

  next();
};

const validateFields = (req, res, next) => {
  if (!req.file) {
    return validationError(res, 'Source file is missing');
  }

  if (req.input.width && typeof req.input.width === 'string') {
    req.input.width = parseInt(req.input.width, 10);

    if (isNaN(req.input.width)) {
      return validationError(res, 'Width must be an integer!');
    }
  }

  if (req.input.height && typeof req.input.height === 'string') {
    req.input.height = parseInt(req.input.height, 10);

    if (isNaN(req.input.height)) {
      return validationError(res, 'height must be an integer!');
    }
  }

  if (req.input.quality) {
    if (typeof req.input.quality === 'string') {
      req.input.quality = parseInt(req.input.quality, 10);
    }

    if (req.input.quality < 1 || req.input.quality > 100) {
      return validationError(res, 'Quality must be an integer between 1-100');
    }
  }

  if (req.input.format) {
    req.input.format = `${req.input.format}`.toLowerCase();

    if (!allowedFormats.has(req.input.format)) {
      return validationError(
        res,
        `Requested output format '${req.input.format}' is not supported!`,
      );
    }
  }

  if (req.input.cropLeft || req.input.cropTop || req.input.cropWidth || req.input.cropHeight) {
    if (typeof req.input.cropLeft === 'string') {
      req.input.cropLeft = parseInt(req.input.cropLeft, 10);
    }

    if (typeof req.input.cropTop === 'string') {
      req.input.cropTop = parseInt(req.input.cropTop, 10);
    }

    if (typeof req.input.cropWidth === 'string') {
      req.input.cropWidth = parseInt(req.input.cropWidth, 10);
    }

    if (typeof req.input.cropHeight === 'string') {
      req.input.cropHeight = parseInt(req.input.cropHeight, 10);
    }

    if (isNaN(req.input.cropLeft)) {
      return validationError(res, 'cropLeft must be an integer!');
    }
    if (isNaN(req.input.cropTop)) {
      return validationError(res, 'cropTop must be an integer!');
    }
    if (isNaN(req.input.cropWidth)) {
      return validationError(res, 'cropWidth must be an integer!');
    }
    if (isNaN(req.input.cropHeight)) {
      return validationError(res, 'cropHeight must be an integer!');
    }
  }

  if (req.input.rotate && typeof req.input.rotate === 'string') {
    req.input.rotate = parseFloat(req.input.rotate);

    if (isNaN(req.input.rotate)) {
      return validationError(res, 'rotate angle must be a number!');
    }
  }

  if (req.input.scaleX) {
    if (typeof req.input.scaleX === 'string') {
      req.input.scaleX = parseFloat(req.input.scaleX);
    }

    if (isNaN(req.input.scaleX) || (req.input.scaleX !== -1 && req.input.scaleX !== 1)) {
      return validationError(res, 'scaleX must be a -1 or 1!');
    }
  }

  if (req.input.scaleY) {
    if (typeof req.input.scaleY === 'string') {
      req.input.scaleY = parseFloat(req.input.scaleY);
    }

    if (isNaN(req.input.scaleY) || (req.input.scaleY !== -1 && req.input.scaleY !== 1)) {
      return validationError(res, 'scaleY must be a -1 or 1!');
    }
  }

  // Background removal validation
  if (req.input.removeBackground) {
    // Parse boolean values
    if (typeof req.input.removeBackground === 'string') {
      req.input.removeBackground =
        req.input.removeBackground.toLowerCase() === 'true' || req.input.removeBackground === '1';
    }

    // If it's just a boolean true, set default options
    if (req.input.removeBackground === true) {
      req.input.removeBackground = {};
    }

    // Validate model if specified
    if (req.input.bgModel && !['small', 'medium'].includes(req.input.bgModel)) {
      return validationError(res, 'Background removal model must be either "small" or "medium"');
    }

    // Validate output format if specified
    if (
      req.input.bgFormat &&
      !['image/png', 'image/jpeg', 'image/webp', 'image/x-rgba8'].includes(req.input.bgFormat)
    ) {
      return validationError(
        res,
        'Background removal output format must be one of: image/png, image/jpeg, image/webp, image/x-rgba8',
      );
    }

    // Validate quality if specified
    if (req.input.bgQuality) {
      if (typeof req.input.bgQuality === 'string') {
        req.input.bgQuality = parseFloat(req.input.bgQuality);
      }
      if (isNaN(req.input.bgQuality) || req.input.bgQuality < 0 || req.input.bgQuality > 1) {
        return validationError(res, 'Background removal quality must be a number between 0 and 1');
      }
    }

    // Validate output type if specified
    if (req.input.bgType && !['foreground', 'background', 'mask'].includes(req.input.bgType)) {
      return validationError(
        res,
        'Background removal output type must be one of: foreground, background, mask',
      );
    }
  }

  next();
};

module.exports = [
  // Parsers
  bodyParser.urlencoded({ extended: false }),
  bodyParser.json(),
  multipart(),

  // Prepare input
  init,
  downloadUrl,

  // Validate input
  fileSize,
  fileMime,
  validateFields,

  // Create ImageInput
  toImageInput,
];
