const healthController = require('./controllers/health');
const inputController = require('./controllers/input');
const backgroundRemovalController = require('./controllers/backgroundRemoval');

// eslint-disable-next-line
const router = require('express').Router();

router.use('/healthz', healthController);
router.use('/remove-background', backgroundRemovalController);
router.use('/', inputController);

module.exports = router;
