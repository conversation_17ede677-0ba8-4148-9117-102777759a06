const router = require('express').Router();

const inputParsers = require('../middlewares/input');
const imageProcessor = require('../services/imageProcessor');
const backgroundRemovalService = require('../services/backgroundRemoval');
const updateStats = require('../middlewares/stats');

router.use(updateStats);

/**
 * Background removal endpoint
 * POST /remove-background
 *
 * Query parameters:
 * - removeBackground: boolean (required) - Enable background removal
 * - bgModel: string (optional) - Model to use ('small' or 'medium', default: 'medium')
 * - bgFormat: string (optional) - Output format ('image/png', 'image/jpeg', 'image/webp', 'image/x-rgba8', default: 'image/png')
 * - bgQuality: number (optional) - Output quality (0-1, default: 0.8)
 * - bgType: string (optional) - Output type ('foreground', 'background', 'mask', default: 'foreground')
 *
 * Body: multipart/form-data with 'src' field containing the image file
 * Or JSON with 'url' field containing image URL
 */
router.post('/', inputParsers, async (req, res) => {
  if (!req.input) {
    return res
      .status(400)
      .json({
        status: 400,
        error: 'No input provided',
      })
      .end();
  }

  // Ensure background removal is enabled
  if (!req.input.removeBackground) {
    req.input.removeBackground = {};
  }

  try {
    const {
      data,
      meta: { output },
    } = await imageProcessor.process(req.input);

    req.log.info({}, 'background removed from image');

    res.output = output;

    res.contentType(output.format);
    res.setHeader('X-Image-Width', output.width);
    res.setHeader('X-Image-Height', output.height);
    res.setHeader('X-Processing-Type', 'background-removal');
    res.write(data);
    res.end();
  } catch (err) {
    req.log.error(err, 'error while removing background from image');

    // Determine appropriate status code based on error type
    let statusCode = 500;
    let errorMessage = err.message;

    if (err.message.includes('Invalid options') || err.message.includes('must be')) {
      statusCode = 400;
    } else if (err.message.includes('download') || err.message.includes('internet connection')) {
      statusCode = 502;
      errorMessage = 'Failed to download background removal model. Please try again later.';
    } else if (err.message.includes('corrupted') || err.message.includes('unsupported format')) {
      statusCode = 422;
      errorMessage = 'The uploaded image appears to be corrupted or in an unsupported format.';
    } else if (err.message.includes('timeout')) {
      statusCode = 504;
      errorMessage = 'Background removal processing timed out. Please try with a smaller image.';
    }

    return res
      .status(statusCode)
      .json({
        status: statusCode,
        error: errorMessage,
        type: 'background-removal-error',
      })
      .end();
  }
});

/**
 * Background removal validation endpoint
 * GET /remove-background/validate
 *
 * Query parameters: same as POST endpoint
 * Returns validation result without processing
 */
router.get('/validate', (req, res) => {
  const options = {};

  if (req.query.bgModel) {
    options.model = req.query.bgModel;
  }

  if (req.query.bgFormat || req.query.bgQuality || req.query.bgType) {
    options.output = {};

    if (req.query.bgFormat) {
      options.output.format = req.query.bgFormat;
    }

    if (req.query.bgQuality) {
      options.output.quality = parseFloat(req.query.bgQuality);
    }

    if (req.query.bgType) {
      options.output.type = req.query.bgType;
    }
  }

  const validation = backgroundRemovalService.validateOptions(options);

  res.json({
    status: validation.isValid ? 200 : 400,
    valid: validation.isValid,
    errors: validation.errors,
    options: options,
  });
});

/**
 * Background removal info endpoint
 * GET /remove-background/info
 *
 * Returns information about available options
 */
router.get('/info', (req, res) => {
  res.json({
    status: 200,
    info: {
      models: ['small', 'medium'],
      outputFormats: ['image/png', 'image/jpeg', 'image/webp', 'image/x-rgba8'],
      outputTypes: ['foreground', 'background', 'mask'],
      qualityRange: { min: 0, max: 1 },
      defaults: {
        model: 'medium',
        format: 'image/png',
        quality: 0.8,
        type: 'foreground',
      },
    },
  });
});

module.exports = router;
