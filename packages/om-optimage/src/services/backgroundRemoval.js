const { removeBackground } = require('@imgly/background-removal-node');
const log = require('./logger');

class BackgroundRemovalService {
  constructor() {
    this.defaultConfig = {
      model: 'medium', // 'small' or 'medium'
      output: {
        format: 'image/png', // 'image/png', 'image/jpeg', 'image/webp', 'image/x-rgba8'
        quality: 0.8,
        type: 'foreground', // 'foreground', 'background', 'mask'
      },
      debug: process.env.NODE_ENV === 'development',
    };
  }

  /**
   * Remove background from image buffer
   * @param {Buffer} imageBuffer - Input image buffer
   * @param {Object} options - Configuration options
   * @returns {Promise<Buffer>} - Processed image buffer
   */
  async removeBackground(imageBuffer, options = {}) {
    try {
      // Validate input buffer
      if (!Buffer.isBuffer(imageBuffer)) {
        throw new Error('Input must be a Buffer');
      }

      if (imageBuffer.length === 0) {
        throw new Error('Input buffer is empty');
      }

      // Validate options
      const validation = this.validateOptions(options);
      if (!validation.isValid) {
        throw new Error(`Invalid options: ${validation.errors.join(', ')}`);
      }

      const config = this._mergeConfig(options);

      log.debug('Starting background removal', {
        model: config.model,
        outputFormat: config.output.format,
        outputType: config.output.type,
        inputSize: imageBuffer.length,
      });

      const startTime = Date.now();

      // Call the background removal function
      const blob = await removeBackground(imageBuffer, config);

      if (!blob) {
        throw new Error('Background removal returned null result');
      }

      // Convert blob to buffer
      const resultBuffer = Buffer.from(await blob.arrayBuffer());

      if (resultBuffer.length === 0) {
        throw new Error('Background removal produced empty result');
      }

      const processingTime = Date.now() - startTime;
      log.info('Background removal completed successfully', {
        processingTime: `${processingTime}ms`,
        inputSize: imageBuffer.length,
        outputSize: resultBuffer.length,
        compressionRatio: (resultBuffer.length / imageBuffer.length).toFixed(2),
      });

      return resultBuffer;
    } catch (error) {
      // Enhanced error logging with context
      log.error('Background removal failed', {
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        inputSize: Buffer.isBuffer(imageBuffer) ? imageBuffer.length : 'invalid',
        options: options,
      });

      // Re-throw with more context
      if (error.message.includes('fetch')) {
        throw new Error(
          'Failed to download background removal model. Please check your internet connection.',
        );
      } else if (error.message.includes('ONNX')) {
        throw new Error(
          'Background removal model processing failed. The image may be corrupted or in an unsupported format.',
        );
      } else if (error.message.includes('Invalid options')) {
        throw error; // Re-throw validation errors as-is
      } else {
        throw new Error(`Background removal failed: ${error.message}`);
      }
    }
  }

  /**
   * Merge user options with default configuration
   * @param {Object} options - User provided options
   * @returns {Object} - Merged configuration
   */
  _mergeConfig(options) {
    return {
      ...this.defaultConfig,
      ...options,
      output: {
        ...this.defaultConfig.output,
        ...(options.output || {}),
      },
    };
  }

  /**
   * Validate background removal options
   * @param {Object} options - Options to validate
   * @returns {Object} - Validation result
   */
  validateOptions(options = {}) {
    const errors = [];

    if (options.model && !['small', 'medium'].includes(options.model)) {
      errors.push('Model must be either "small" or "medium"');
    }

    if (options.output) {
      const { format, quality, type } = options.output;

      if (format && !['image/png', 'image/jpeg', 'image/webp', 'image/x-rgba8'].includes(format)) {
        errors.push(
          'Output format must be one of: image/png, image/jpeg, image/webp, image/x-rgba8',
        );
      }

      if (quality !== undefined && (typeof quality !== 'number' || quality < 0 || quality > 1)) {
        errors.push('Quality must be a number between 0 and 1');
      }

      if (type && !['foreground', 'background', 'mask'].includes(type)) {
        errors.push('Output type must be one of: foreground, background, mask');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

module.exports = new BackgroundRemovalService();
module.exports.BackgroundRemovalService = BackgroundRemovalService;
