const { DEFAULT_IMAGE_QUALITY } = process.env;

const defaultImageQuality = parseInt(DEFAULT_IMAGE_QUALITY, 10) || 80;

class ImageInput {
  constructor(img, init = { quality: defaultImageQuality }) {
    if (!Buffer.isBuffer(img)) {
      throw new Error('Provided image is not a buffer!');
    }
    this.image = img;

    this.quality = init.quality || defaultImageQuality;

    if (init.format) {
      this.format = init.format;
    }
    if (init.rotate) {
      let deg = init.rotate;

      if (typeof deg === 'string') {
        deg = parseFloat(deg);
      }

      if (deg) {
        this.rotate = deg;
      }
    }
    if (init.width || init.height) {
      const width = parseInt(init.width, 10);
      const height = parseInt(init.height, 10);

      if (width || height) {
        this.resize = {
          width: width || null,
          height: height || null,
        };
      }
    }
    if (init.cropWidth || init.cropHeight || init.cropLeft || init.cropTop) {
      const width = parseInt(init.cropWidth, 10);
      const height = parseInt(init.cropHeight, 10);
      const top = parseInt(init.cropTop, 10);
      const left = parseInt(init.cropLeft, 10);

      if (width || height) {
        this.crop = {
          width: width || null,
          height: height || null,
          top: top || 0,
          left: left || 0,
        };
      }
    }
    if (init.scale) {
      const scale = parseFloat(init.scale);

      if (scale) {
        this.scale = {
          x: scale,
          y: scale,
        };
      }
    }
    if (init.scaleX || init.scaleY) {
      const scaleX = parseFloat(init.scaleX);
      const scaleY = parseFloat(init.scaleY);

      if (scaleX) {
        this.scale = this.scale || {};
        this.scale.x = scaleX;
      }
      if (scaleY) {
        this.scale = this.scale || {};
        this.scale.y = scaleY;
      }
    }

    // Background removal configuration
    if (init.removeBackground) {
      this.removeBackground = {};

      // Set model if specified
      if (init.bgModel) {
        this.removeBackground.model = init.bgModel;
      }

      // Set output configuration
      if (init.bgFormat || init.bgQuality || init.bgType) {
        this.removeBackground.output = {};

        if (init.bgFormat) {
          this.removeBackground.output.format = init.bgFormat;
        }

        if (init.bgQuality !== undefined) {
          this.removeBackground.output.quality = init.bgQuality;
        }

        if (init.bgType) {
          this.removeBackground.output.type = init.bgType;
        }
      }
    }
  }

  getBuffer() {
    return this.image;
  }
}

module.exports = ImageInput;
